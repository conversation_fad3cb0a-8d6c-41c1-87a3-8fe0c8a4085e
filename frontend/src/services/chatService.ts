/**
 * Chat service
 * Handles chat messages, conversation history, and session management
 */
import { httpBase, ApiCallbacks } from './baseHttp';
import { ChatRequest, ChatResponse, ConversationHistory, UserAnalytics } from '../types/api';

class ChatService {
  private readonly CHAT_ENDPOINTS = {
    SEND_MESSAGE: '/api/chat/',
    PLAYGROUND: '/api/chat/',  // Use same endpoint for now, differentiate by payload
    GET_CONVERSATIONS: '/api/chat/conversations',
    GET_SESSIONS: '/api/chat/sessions',
    GET_HISTORY: '/api/chat/history',
    GET_ANALYTICS: '/api/chat/analytics',
    DELETE_SESSION: '/api/chat/session',
  };

  /**
   * Send a chat message
   */
  async sendMessage(
    request: ChatRequest,
    callbacks?: ApiCallbacks<ChatResponse>
  ): Promise<ChatResponse | null> {
    try {
      const response = await httpBase.post<ChatResponse>(
        this.CHAT_ENDPOINTS.SEND_MESSAGE,
        request,
        {},
        {
          onSuccess: (data) => {
            console.log('✅ Message sent successfully');
            callbacks?.onSuccess?.(data);
          },
          onFailure: (error) => {
            console.error('❌ Failed to send message:', error.message);
            callbacks?.onFailure?.(error);
          },
          onFinally: callbacks?.onFinally,
        }
      );

      return response;
    } catch (error) {
      return null;
    }
  }

  /**
   * Send a playground message (no history saved)
   */
  async sendPlaygroundMessage(
    message: string,
    callbacks?: ApiCallbacks<ChatResponse>
  ): Promise<ChatResponse | null> {
    try {
      const response = await httpBase.post<ChatResponse>(
        this.CHAT_ENDPOINTS.PLAYGROUND,
        { message, playground: true },
        {},
        {
          onSuccess: (data) => {
            console.log('✅ Playground message sent successfully');
            callbacks?.onSuccess?.(data);
          },
          onFailure: (error) => {
            console.error('❌ Failed to send playground message:', error.message);
            callbacks?.onFailure?.(error);
          },
          onFinally: callbacks?.onFinally,
        }
      );

      return response;
    } catch (error) {
      return null;
    }
  }

  /**
   * Get user's conversations
   */
  async getConversations(
    callbacks?: ApiCallbacks<{ conversations: Array<any> }>
  ): Promise<{ conversations: Array<any> } | null> {
    try {
      const response = await httpBase.get(
        this.CHAT_ENDPOINTS.GET_CONVERSATIONS,
        {},
        {
          onSuccess: (data) => {
            console.log(`✅ Loaded ${data.conversations?.length || 0} conversations`);
            callbacks?.onSuccess?.(data);
          },
          onFailure: (error) => {
            console.error('❌ Failed to load conversations:', error.message);
            callbacks?.onFailure?.(error);
          },
          onFinally: callbacks?.onFinally,
        }
      );

      return response;
    } catch (error) {
      return null;
    }
  }

  /**
   * Get user's chat sessions
   */
  async getUserSessions(
    callbacks?: ApiCallbacks<{ sessions: Array<{ session_id: string; last_message: string }>; total: number }>
  ): Promise<{ sessions: Array<{ session_id: string; last_message: string }>; total: number } | null> {
    try {
      const response = await httpBase.get(
        this.CHAT_ENDPOINTS.GET_SESSIONS,
        {},
        {
          onSuccess: (data) => {
            console.log(`✅ Loaded ${data.total} chat sessions`);
            callbacks?.onSuccess?.(data);
          },
          onFailure: (error) => {
            console.error('❌ Failed to load chat sessions:', error.message);
            callbacks?.onFailure?.(error);
          },
          onFinally: callbacks?.onFinally,
        }
      );

      return response;
    } catch (error) {
      return null;
    }
  }

  /**
   * Get conversation history for a session
   */
  async getConversationHistory(
    sessionId: string,
    limit: number = 50,
    callbacks?: ApiCallbacks<ConversationHistory>
  ): Promise<ConversationHistory | null> {
    try {
      const response = await httpBase.get<ConversationHistory>(
        `${this.CHAT_ENDPOINTS.GET_HISTORY}/${sessionId}`,
        { params: { limit } },
        {
          onSuccess: (data) => {
            console.log(`✅ Loaded ${data.messages.length} messages for session ${sessionId}`);
            callbacks?.onSuccess?.(data);
          },
          onFailure: (error) => {
            console.error('❌ Failed to load conversation history:', error.message);
            callbacks?.onFailure?.(error);
          },
          onFinally: callbacks?.onFinally,
        }
      );

      return response;
    } catch (error) {
      return null;
    }
  }

  /**
   * Get user analytics
   */
  async getUserAnalytics(
    callbacks?: ApiCallbacks<UserAnalytics>
  ): Promise<UserAnalytics | null> {
    try {
      const response = await httpBase.get<UserAnalytics>(
        this.CHAT_ENDPOINTS.GET_ANALYTICS,
        {},
        {
          onSuccess: (data) => {
            console.log('✅ User analytics loaded');
            callbacks?.onSuccess?.(data);
          },
          onFailure: (error) => {
            console.error('❌ Failed to load user analytics:', error.message);
            callbacks?.onFailure?.(error);
          },
          onFinally: callbacks?.onFinally,
        }
      );

      return response;
    } catch (error) {
      return null;
    }
  }

  /**
   * Delete a chat session
   */
  async deleteSession(
    sessionId: string,
    callbacks?: ApiCallbacks<{ message: string }>
  ): Promise<boolean> {
    try {
      await httpBase.delete(
        `${this.CHAT_ENDPOINTS.DELETE_SESSION}/${sessionId}`,
        {},
        {
          onSuccess: (data) => {
            console.log(`✅ Session ${sessionId} deleted`);
            callbacks?.onSuccess?.(data);
          },
          onFailure: (error) => {
            console.error('❌ Failed to delete session:', error.message);
            callbacks?.onFailure?.(error);
          },
          onFinally: callbacks?.onFinally,
        }
      );

      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * Generate a new session ID
   */
  generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Format message timestamp
   */
  formatMessageTime(timestamp: string): string {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 1) {
      const minutes = Math.floor(diffInHours * 60);
      return minutes <= 1 ? 'Just now' : `${minutes} minutes ago`;
    } else if (diffInHours < 24) {
      const hours = Math.floor(diffInHours);
      return `${hours} hour${hours > 1 ? 's' : ''} ago`;
    } else {
      return date.toLocaleDateString();
    }
  }

  /**
   * Validate message content
   */
  validateMessage(message: string): { isValid: boolean; message: string } {
    if (!message || message.trim().length === 0) {
      return { isValid: false, message: 'Message cannot be empty' };
    }
    if (message.length > 5000) {
      return { isValid: false, message: 'Message is too long (max 5000 characters)' };
    }
    return { isValid: true, message: 'Message is valid' };
  }

  /**
   * Extract session ID from URL or generate new one
   */
  getOrCreateSessionId(): string {
    const urlParams = new URLSearchParams(window.location.search);
    const sessionId = urlParams.get('session');
    
    if (sessionId) {
      return sessionId;
    }
    
    const newSessionId = this.generateSessionId();
    // Update URL without page reload
    const newUrl = new URL(window.location.href);
    newUrl.searchParams.set('session', newSessionId);
    window.history.replaceState({}, '', newUrl.toString());
    
    return newSessionId;
  }
}

// Export singleton instance
export const chatService = new ChatService();
export default chatService;
